# Project Kickoff Template

**Instructions**: Fill out all sections below. Once complete, share this with Augment Agent to begin the Discovery & Planning phase.

## Project Overview

### App Concept
**What is your app idea?**
```
[Describe your app in 2-3 sentences - what does it do and why?]
```

**What problem does it solve?**
```
[Explain the specific problem or pain point your app addresses]
```

**What makes it unique?**
```
[How is it different from existing solutions?]
```

## Target Users & Market

### Primary Users
**Who will use this app?**
```
[Describe your target users - demographics, roles, characteristics]
```

**How will they use it?**
```
[Describe typical user scenarios and use cases]
```

### Market Context
**Do you have competitors in mind?**
```
[List any apps/services you see as competitors or inspiration]
```

**What's your target market size?**
```
[Local, national, global? Niche or broad market?]
```

## Business Requirements

### Core Features (Must-Have)
**What are the essential features your app MUST have?**
```
1. [Feature 1 - brief description]
2. [Feature 2 - brief description]
3. [Feature 3 - brief description]
4. [Add more as needed]
```

### Nice-to-Have Features
**What features would be great to have but aren't essential for launch?**
```
1. [Feature 1 - brief description]
2. [Feature 2 - brief description]
3. [Add more as needed]
```

### Success Metrics
**How will you measure if the app is successful?**
```
[Examples: number of users, revenue targets, user engagement metrics, etc.]
```

## Technical Preferences

### Platform
**What platforms do you want to target?**
```
□ Web application
□ iOS mobile app
□ Android mobile app
□ Desktop application
□ Other: [specify]
```

### Technical Constraints
**Do you have any specific technical requirements or preferences?**
```
[Examples: must integrate with specific services, preferred programming languages, 
hosting preferences, security requirements, etc.]
```

**Any technical limitations or constraints?**
```
[Examples: budget limitations affecting hosting, compliance requirements, 
existing systems that must be integrated, etc.]
```

## Project Constraints

### Timeline
**When do you want to launch?**
```
[Specific date or timeframe - be realistic]
```

**Are there any important deadlines or milestones?**
```
[Examples: demo dates, funding deadlines, market events, etc.]
```

### Budget
**What's your development budget range?**
```
□ Under $5,000
□ $5,000 - $15,000
□ $15,000 - $50,000
□ $50,000+
□ No specific budget limit
```

**Any ongoing operational budget considerations?**
```
[Examples: hosting costs, third-party service fees, maintenance budget]
```

### Resources
**What resources do you have available?**
```
[Examples: your time availability, team members, existing assets, 
content, designs, etc.]
```

## Business Context

### Your Role
**What's your background and role in this project?**
```
[Examples: business owner, technical founder, project manager, etc.]
```

**How much time can you dedicate to this project weekly?**
```
[Be realistic - this affects our collaboration rhythm]
```

### Decision Making
**Who needs to approve major decisions?**
```
[Examples: just you, you + co-founder, board approval needed, etc.]
```

**What decisions do you want to be involved in vs. delegate?**
```
[Examples: all UI decisions, only major architecture choices, 
budget-related decisions, etc.]
```

## Quality & Compliance

### Quality Standards
**What quality standards are important to you?**
```
[Examples: enterprise-grade security, high performance, accessibility, 
mobile responsiveness, etc.]
```

### Compliance Requirements
**Are there any regulatory or compliance requirements?**
```
[Examples: GDPR, HIPAA, SOC2, industry-specific regulations]
```

## Communication Preferences

### Check-in Frequency
**How often do you want progress updates?**
```
□ Daily brief updates
□ Every 2-3 days
□ Weekly comprehensive updates
□ As-needed basis
```

### Review Process
**How do you prefer to review and approve work?**
```
[Examples: live demos, written reports, screenshots, 
working prototypes, etc.]
```

### Availability
**When are you typically available for discussions?**
```
[Time zones, preferred days/times, response time expectations]
```

---

## Completion Checklist

Before submitting this template, ensure you've filled out:

□ App concept and problem statement
□ Target users and market context
□ Core features (must-have list)
□ Platform and technical preferences
□ Timeline and budget constraints
□ Your role and availability
□ Quality and compliance requirements
□ Communication preferences

**Once complete, share this with Augment Agent to begin development!**
